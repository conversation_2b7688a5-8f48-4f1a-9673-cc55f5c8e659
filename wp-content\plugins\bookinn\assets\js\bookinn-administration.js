/**
 * BookInn Administration Widget JavaScript
 * Following the exact same patterns as Management Center widget
 */

(function($) {
    'use strict';

    // Namespace for Administration functionality
    window.BookInn = window.BookInn || {};
    window.BookInn.Administration = {
        
        // Configuration
        config: {
            ajaxUrl: bookinnAdmin.ajax_url,
            nonce: bookinnAdmin.nonce,
            strings: bookinnAdmin.strings
        },
        
        // Current state
        currentTab: 'frontend-settings',
        currentSubtab: 'hotel-management',
        initialized: false,
        
        /**
         * Initialize the Administration widget
         */
        init: function() {
            if (this.initialized) {
                console.log('BookInn Administration: Already initialized');
                return;
            }
            
            console.log('BookInn Administration: Initializing...');
            
            this.bindEvents();
            this.initializeTabs();
            this.loadDefaultContent();
            
            this.initialized = true;
            console.log('BookInn Administration: Initialized successfully');
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            const self = this;
            
            // Main tab navigation
            $(document).on('click', '.bookinn-administration-nav .bookinn-tab-link', function(e) {
                e.preventDefault();
                const tabId = $(this).data('tab');
                self.switchTab(tabId);
            });
            
            // Vertical sub-tab navigation
            $(document).on('click', '.bookinn-vertical-tab-link', function(e) {
                e.preventDefault();
                const subtabId = $(this).data('subtab');
                self.switchSubtab(subtabId);
            });
            
            // Refresh button
            $(document).on('click', '#bookinn-refresh-admin', function(e) {
                e.preventDefault();
                self.refreshCurrentContent();
            });
            
            // Hotel settings form
            $(document).on('click', '#save-hotel-settings', function(e) {
                e.preventDefault();
                self.saveHotelSettings();
            });
            
            // User management actions
            $(document).on('click', '#add-new-user', function(e) {
                e.preventDefault();
                self.showAddUserModal();
            });
            
            $(document).on('click', '.edit-user', function(e) {
                e.preventDefault();
                const userId = $(this).data('user-id');
                self.showEditUserModal(userId);
            });
            
            $(document).on('click', '.delete-user', function(e) {
                e.preventDefault();
                const userId = $(this).data('user-id');
                self.deleteUser(userId);
            });
            
            // OTA management actions
            $(document).on('click', '#refresh-ota-overview', function(e) {
                e.preventDefault();
                self.loadOtaOverview();
            });
            
            $(document).on('click', '#add-ota-channel', function(e) {
                e.preventDefault();
                self.showAddChannelModal();
            });
            
            $(document).on('click', '.sync-channel', function(e) {
                e.preventDefault();
                const channelId = $(this).data('channel-id');
                self.syncChannel(channelId);
            });
            
            $(document).on('click', '#save-room-mapping', function(e) {
                e.preventDefault();
                self.saveRoomMapping();
            });
            
            $(document).on('click', '#refresh-sync-status', function(e) {
                e.preventDefault();
                self.loadSyncStatus();
            });
            
            $(document).on('click', '#refresh-activity-logs', function(e) {
                e.preventDefault();
                self.loadActivityLogs();
            });
            
            $(document).on('click', '#clear-activity-logs', function(e) {
                e.preventDefault();
                self.clearActivityLogs();
            });
        },
        
        /**
         * Initialize tabs based on default settings
         */
        initializeTabs: function() {
            const container = $('.bookinn-administration-container');
            const defaultTab = container.data('default-tab') || 'frontend-settings';
            
            this.switchTab(defaultTab);
        },
        
        /**
         * Switch main tab
         */
        switchTab: function(tabId) {
            console.log('BookInn Administration: Switching to tab', tabId);
            
            // Update navigation
            $('.bookinn-tab-link').removeClass('active');
            $(`.bookinn-tab-link[data-tab="${tabId}"]`).addClass('active');
            
            // Update content
            $('.bookinn-tab-content').removeClass('active');
            $(`#${tabId}`).addClass('active');
            
            this.currentTab = tabId;
            
            // Load content for the tab
            this.loadTabContent(tabId);
        },
        
        /**
         * Switch vertical sub-tab
         */
        switchSubtab: function(subtabId) {
            console.log('BookInn Administration: Switching to subtab', subtabId);
            
            // Update navigation
            $('.bookinn-vertical-tab-link').removeClass('active');
            $(`.bookinn-vertical-tab-link[data-subtab="${subtabId}"]`).addClass('active');
            
            // Update content
            $('.bookinn-vertical-panel').removeClass('active');
            $(`#${subtabId}`).addClass('active');
            
            this.currentSubtab = subtabId;
            
            // Load content for the subtab
            this.loadSubtabContent(subtabId);
        },
        
        /**
         * Load default content
         */
        loadDefaultContent: function() {
            // Load content for the default tab and subtab
            this.loadTabContent(this.currentTab);
        },
        
        /**
         * Load content for a specific tab
         */
        loadTabContent: function(tabId) {
            switch(tabId) {
                case 'frontend-settings':
                    // Load default subtab content
                    this.loadSubtabContent('hotel-management');
                    break;
                case 'ota-management':
                    // Load default subtab content
                    this.loadSubtabContent('ota-overview');
                    break;
            }
        },
        
        /**
         * Load content for a specific subtab
         */
        loadSubtabContent: function(subtabId) {
            switch(subtabId) {
                case 'hotel-management':
                    this.loadHotelSettings();
                    break;
                case 'user-management':
                    this.loadUserManagement();
                    break;
                case 'ota-overview':
                    this.loadOtaOverview();
                    break;
                case 'ota-channels':
                    this.loadOtaChannels();
                    break;
                case 'room-mapping':
                    this.loadRoomMapping();
                    break;
                case 'sync-status':
                    this.loadSyncStatus();
                    break;
                case 'activity-logs':
                    this.loadActivityLogs();
                    break;
            }
        },
        
        /**
         * Refresh current content
         */
        refreshCurrentContent: function() {
            this.loadSubtabContent(this.currentSubtab);
        },
        
        /**
         * Show loading state
         */
        showLoading: function(containerId) {
            $(`#${containerId}-loading`).show();
            $(`#${containerId}-content`).hide();
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function(containerId) {
            $(`#${containerId}-loading`).hide();
            $(`#${containerId}-content`).show();
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            if (window.BookInn && window.BookInn.Core && window.BookInn.Core.showNotification) {
                window.BookInn.Core.showNotification('error', message);
            } else {
                alert(message);
            }
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message) {
            if (window.BookInn && window.BookInn.Core && window.BookInn.Core.showNotification) {
                window.BookInn.Core.showNotification('success', message);
            } else {
                alert(message);
            }
        },
        
        /**
         * Make AJAX request
         */
        ajaxRequest: function(action, data, callback) {
            const self = this;

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: $.extend({
                    action: action,
                    nonce: this.config.nonce
                }, data),
                success: function(response) {
                    if (response.success) {
                        callback(response.data);
                    } else {
                        self.showError(response.data.message || self.config.strings.error);
                    }
                },
                error: function() {
                    self.showError(self.config.strings.error);
                }
            });
        },

        // ===== FRONTEND SETTINGS METHODS =====

        /**
         * Load hotel settings
         */
        loadHotelSettings: function() {
            const self = this;
            this.showLoading('hotel-settings');

            this.ajaxRequest('bookinn_get_hotel_settings', {}, function(data) {
                $('#hotel-settings-content').html(data.html);
                self.hideLoading('hotel-settings');
            });
        },

        /**
         * Save hotel settings
         */
        saveHotelSettings: function() {
            const self = this;
            const form = $('#hotel-settings-form');
            const formData = form.serialize();

            this.ajaxRequest('bookinn_save_hotel_settings', formData, function(data) {
                self.showSuccess(data.message);
            });
        },

        /**
         * Load user management
         */
        loadUserManagement: function() {
            const self = this;
            this.showLoading('user-management');

            this.ajaxRequest('bookinn_get_user_management', {}, function(data) {
                $('#user-management-content').html(data.html);
                self.hideLoading('user-management');
            });
        },

        /**
         * Show add user modal
         */
        showAddUserModal: function() {
            // Implementation for add user modal
            console.log('Show add user modal');
        },

        /**
         * Show edit user modal
         */
        showEditUserModal: function(userId) {
            // Implementation for edit user modal
            console.log('Show edit user modal for user:', userId);
        },

        /**
         * Delete user
         */
        deleteUser: function(userId) {
            if (confirm(this.config.strings.confirm_delete)) {
                // Implementation for delete user
                console.log('Delete user:', userId);
            }
        },

        // ===== OTA MANAGEMENT METHODS =====

        /**
         * Load OTA overview
         */
        loadOtaOverview: function() {
            const self = this;
            this.showLoading('ota-overview');

            this.ajaxRequest('bookinn_get_ota_overview', {}, function(data) {
                $('#ota-overview-content').html(data.html);
                self.hideLoading('ota-overview');
            });
        },

        /**
         * Load OTA channels
         */
        loadOtaChannels: function() {
            const self = this;
            this.showLoading('ota-channels');

            this.ajaxRequest('bookinn_get_ota_channels', {}, function(data) {
                $('#ota-channels-content').html(data.html);
                self.hideLoading('ota-channels');
            });
        },

        /**
         * Show add channel modal
         */
        showAddChannelModal: function() {
            // Implementation for add channel modal
            console.log('Show add channel modal');
        },

        /**
         * Sync channel
         */
        syncChannel: function(channelId) {
            const self = this;
            console.log('Syncing channel:', channelId);

            // Show loading state for the specific channel
            $(`.sync-channel[data-channel-id="${channelId}"]`).prop('disabled', true).html('<i class="fa-solid fa-spinner fa-spin bookinn-fa"></i> Syncing...');

            this.ajaxRequest('bookinn_sync_channel', { channel_id: channelId }, function(data) {
                self.showSuccess('Channel synced successfully');
                self.loadOtaChannels(); // Reload channels
            });
        },

        /**
         * Load room mapping
         */
        loadRoomMapping: function() {
            const self = this;
            this.showLoading('room-mapping');

            // Mock room mapping content for now
            setTimeout(function() {
                $('#room-mapping-content').html('<p>Room mapping interface will be implemented here.</p>');
                self.hideLoading('room-mapping');
            }, 1000);
        },

        /**
         * Save room mapping
         */
        saveRoomMapping: function() {
            const self = this;
            const mappingData = {}; // Collect mapping data from form

            this.ajaxRequest('bookinn_save_room_mapping', { mapping_data: mappingData }, function(data) {
                self.showSuccess(data.message);
            });
        },

        /**
         * Load sync status
         */
        loadSyncStatus: function() {
            const self = this;
            this.showLoading('sync-status');

            this.ajaxRequest('bookinn_get_sync_status', {}, function(data) {
                $('#sync-status-content').html(data.html);
                self.hideLoading('sync-status');
            });
        },

        /**
         * Load activity logs
         */
        loadActivityLogs: function() {
            const self = this;
            this.showLoading('activity-logs');

            this.ajaxRequest('bookinn_get_activity_logs', {}, function(data) {
                $('#activity-logs-content').html(data.html);
                self.hideLoading('activity-logs');
            });
        },

        /**
         * Clear activity logs
         */
        clearActivityLogs: function() {
            if (confirm('Are you sure you want to clear all activity logs?')) {
                const self = this;

                this.ajaxRequest('bookinn_clear_activity_logs', {}, function(data) {
                    self.showSuccess('Activity logs cleared successfully');
                    self.loadActivityLogs(); // Reload logs
                });
            }
        }
    };

    // Auto-initialize when DOM is ready
    $(document).ready(function() {
        console.log('BookInn Administration: DOM ready');

        // Use MutationObserver to detect when administration widget is added
        let initialized = false;
        let observer = null;

        function tryInit() {
            if (!initialized && $('.bookinn-administration-container').length > 0) {
                BookInn.Administration.init();
                initialized = true;
                if (observer) {
                    observer.disconnect();
                }
                console.log('BookInn Administration: Initialized via observer');
            }
        }

        // Initial check
        tryInit();

        // Only create observer if not already initialized
        if (!initialized) {
            observer = new MutationObserver(tryInit);
            observer.observe(document.body, { childList: true, subtree: true });
        }
    });

    // Expose to global scope for debugging
    window.BookInnAdministration = BookInn.Administration;

})(jQuery);
