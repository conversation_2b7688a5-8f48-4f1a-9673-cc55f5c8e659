<?php
/**
 * BookInn Administration Widget Test Page
 * 
 * This file provides a simple test interface to verify the Administration widget
 * is working correctly and all components are properly loaded.
 * 
 * Usage: Add this file to your WordPress theme and access via:
 * yoursite.com/wp-content/plugins/bookinn/test-administration-widget.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Check if user has admin permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

// Enqueue required assets
wp_enqueue_style('bookinn-management-unified', BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css', array(), BOOKINN_VERSION);
wp_enqueue_style('bookinn-administration', BOOKINN_PLUGIN_URL . 'assets/css/bookinn-administration.css', array('bookinn-management-unified'), BOOKINN_VERSION);

wp_enqueue_script('jquery');
wp_enqueue_script('bookinn-main', BOOKINN_PLUGIN_URL . 'assets/js/bookinn-main.js', array('jquery'), BOOKINN_VERSION, true);
wp_enqueue_script('bookinn-management-unified', BOOKINN_PLUGIN_URL . 'assets/js/bookinn-management-unified.js', array('jquery', 'bookinn-main'), BOOKINN_VERSION, true);
wp_enqueue_script('bookinn-administration', BOOKINN_PLUGIN_URL . 'assets/js/bookinn-administration.js', array('jquery', 'bookinn-main', 'bookinn-management-unified'), BOOKINN_VERSION, true);

// Localize script
wp_localize_script('bookinn-administration', 'bookinnAdmin', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('bookinn_admin_nonce'),
    'strings' => array(
        'loading' => __('Loading...', 'bookinn'),
        'error' => __('An error occurred', 'bookinn'),
        'success' => __('Settings saved successfully', 'bookinn'),
        'confirm_delete' => __('Are you sure you want to delete this item?', 'bookinn'),
    )
));

get_header();
?>

<div class="wrap">
    <h1>BookInn Administration Widget Test</h1>
    
    <div style="margin: 20px 0; padding: 15px; background: #f0f0f1; border-left: 4px solid #72aee6;">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Verify that the widget loads without JavaScript errors (check browser console)</li>
            <li>Test tab navigation between Frontend Settings and OTA Management</li>
            <li>Test vertical sub-tab navigation within each section</li>
            <li>Verify AJAX loading states work correctly</li>
            <li>Check responsive design by resizing browser window</li>
            <li>Confirm all buttons and forms are properly styled</li>
        </ol>
    </div>

    <!-- Administration Widget Test Container -->
    <div class="bookinn-administration-container" data-default-tab="frontend-settings">
        <!-- Administration Header -->
        <div class="bookinn-administration-header">
            <div class="bookinn-header-content">
                <img src="<?php echo BOOKINN_PLUGIN_URL . 'assets/images/logo_header_widget.png'; ?>" alt="BookInn Logo" class="bookinn-logo">
                <h2 class="bookinn-administration-title"><?php _e('Administration Panel', 'bookinn'); ?></h2>
                <div class="bookinn-header-actions">
                    <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-admin">
                        <i class="fa-solid fa-refresh bookinn-fa"></i>
                        <?php _e('Refresh', 'bookinn'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Tab Navigation -->
        <nav class="bookinn-administration-nav">
            <ul class="bookinn-tab-nav" role="tablist">
                <li class="bookinn-tab-item">
                    <a href="#frontend-settings" class="bookinn-tab-link active" role="tab" data-tab="frontend-settings">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Frontend Settings', 'bookinn'); ?>
                    </a>
                </li>
                <li class="bookinn-tab-item">
                    <a href="#ota-management" class="bookinn-tab-link" role="tab" data-tab="ota-management">
                        <span class="dashicons dashicons-networking"></span>
                        <?php _e('OTA Management', 'bookinn'); ?>
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- Tab Content -->
        <div class="bookinn-administration-content">
            <!-- Frontend Settings Tab -->
            <div id="frontend-settings" class="bookinn-tab-content active" role="tabpanel">
                <div class="bookinn-frontend-settings-container">
                    <!-- Vertical Sub-tabs Navigation -->
                    <div class="bookinn-vertical-tabs">
                        <div class="bookinn-vertical-nav">
                            <a href="#hotel-management" class="bookinn-vertical-tab-link active" data-subtab="hotel-management">
                                <i class="fa-solid fa-building bookinn-fa"></i>
                                <?php _e('Hotel Management', 'bookinn'); ?>
                            </a>
                            <a href="#user-management" class="bookinn-vertical-tab-link" data-subtab="user-management">
                                <i class="fa-solid fa-users bookinn-fa"></i>
                                <?php _e('User Management', 'bookinn'); ?>
                            </a>
                        </div>
                        
                        <!-- Vertical Sub-tabs Content -->
                        <div class="bookinn-vertical-content">
                            <!-- Hotel Management Sub-tab -->
                            <div class="bookinn-vertical-panel active" id="hotel-management">
                                <div class="bookinn-hotel-management-content">
                                    <div class="bookinn-section-header">
                                        <h3><?php _e('Hotel Settings', 'bookinn'); ?></h3>
                                        <button class="bookinn-btn bookinn-btn-primary" id="save-hotel-settings">
                                            <i class="fa-solid fa-save bookinn-fa"></i>
                                            <?php _e('Save Settings', 'bookinn'); ?>
                                        </button>
                                    </div>
                                    
                                    <div class="bookinn-loading-placeholder" id="hotel-settings-loading">
                                        <div class="bookinn-spinner"></div>
                                        <p><?php _e('Loading hotel settings...', 'bookinn'); ?></p>
                                    </div>
                                    
                                    <div class="bookinn-content-area" id="hotel-settings-content" style="display: none;">
                                        <!-- Hotel settings form will be loaded here via AJAX -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- User Management Sub-tab -->
                            <div class="bookinn-vertical-panel" id="user-management">
                                <div class="bookinn-user-management-content">
                                    <div class="bookinn-section-header">
                                        <h3><?php _e('User Management', 'bookinn'); ?></h3>
                                        <button class="bookinn-btn bookinn-btn-primary" id="add-new-user">
                                            <i class="fa-solid fa-plus bookinn-fa"></i>
                                            <?php _e('Add User', 'bookinn'); ?>
                                        </button>
                                    </div>
                                    
                                    <div class="bookinn-loading-placeholder" id="user-management-loading">
                                        <div class="bookinn-spinner"></div>
                                        <p><?php _e('Loading user management...', 'bookinn'); ?></p>
                                    </div>
                                    
                                    <div class="bookinn-content-area" id="user-management-content" style="display: none;">
                                        <!-- User management content will be loaded here via AJAX -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- OTA Management Tab -->
            <div id="ota-management" class="bookinn-tab-content" role="tabpanel">
                <div class="bookinn-ota-management-container">
                    <!-- Vertical Sub-tabs Navigation -->
                    <div class="bookinn-vertical-tabs">
                        <div class="bookinn-vertical-nav">
                            <a href="#ota-overview" class="bookinn-vertical-tab-link active" data-subtab="ota-overview">
                                <i class="fa-solid fa-dashboard bookinn-fa"></i>
                                <?php _e('Overview', 'bookinn'); ?>
                            </a>
                            <a href="#ota-channels" class="bookinn-vertical-tab-link" data-subtab="ota-channels">
                                <i class="fa-solid fa-broadcast-tower bookinn-fa"></i>
                                <?php _e('Channels', 'bookinn'); ?>
                            </a>
                            <a href="#room-mapping" class="bookinn-vertical-tab-link" data-subtab="room-mapping">
                                <i class="fa-solid fa-sitemap bookinn-fa"></i>
                                <?php _e('Room Mapping', 'bookinn'); ?>
                            </a>
                            <a href="#sync-status" class="bookinn-vertical-tab-link" data-subtab="sync-status">
                                <i class="fa-solid fa-sync bookinn-fa"></i>
                                <?php _e('Sync Status', 'bookinn'); ?>
                            </a>
                            <a href="#activity-logs" class="bookinn-vertical-tab-link" data-subtab="activity-logs">
                                <i class="fa-solid fa-list bookinn-fa"></i>
                                <?php _e('Activity Logs', 'bookinn'); ?>
                            </a>
                        </div>
                        
                        <!-- Vertical Sub-tabs Content -->
                        <div class="bookinn-vertical-content">
                            <!-- OTA Overview Sub-tab -->
                            <div class="bookinn-vertical-panel active" id="ota-overview">
                                <div class="bookinn-ota-overview-content">
                                    <div class="bookinn-section-header">
                                        <h3><?php _e('OTA Overview', 'bookinn'); ?></h3>
                                        <button class="bookinn-btn bookinn-btn-secondary" id="refresh-ota-overview">
                                            <i class="fa-solid fa-refresh bookinn-fa"></i>
                                            <?php _e('Refresh', 'bookinn'); ?>
                                        </button>
                                    </div>
                                    
                                    <div class="bookinn-loading-placeholder" id="ota-overview-loading">
                                        <div class="bookinn-spinner"></div>
                                        <p><?php _e('Loading OTA overview...', 'bookinn'); ?></p>
                                    </div>
                                    
                                    <div class="bookinn-content-area" id="ota-overview-content" style="display: none;">
                                        <!-- OTA overview content will be loaded here via AJAX -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Other OTA sub-tabs would be here -->
                            <div class="bookinn-vertical-panel" id="ota-channels">
                                <div class="bookinn-ota-channels-content">
                                    <div class="bookinn-section-header">
                                        <h3><?php _e('OTA Channels', 'bookinn'); ?></h3>
                                        <button class="bookinn-btn bookinn-btn-primary" id="add-ota-channel">
                                            <i class="fa-solid fa-plus bookinn-fa"></i>
                                            <?php _e('Add Channel', 'bookinn'); ?>
                                        </button>
                                    </div>
                                    
                                    <div class="bookinn-loading-placeholder" id="ota-channels-loading">
                                        <div class="bookinn-spinner"></div>
                                        <p><?php _e('Loading OTA channels...', 'bookinn'); ?></p>
                                    </div>
                                    
                                    <div class="bookinn-content-area" id="ota-channels-content" style="display: none;">
                                        <!-- OTA channels content will be loaded here via AJAX -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Additional sub-tabs would follow the same pattern -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="margin: 20px 0; padding: 15px; background: #fff; border: 1px solid #ccd0d4;">
        <h3>Debug Information:</h3>
        <p><strong>Widget Class:</strong> <?php echo class_exists('BookInn_Widget_Administration') ? 'Loaded ✅' : 'Not Found ❌'; ?></p>
        <p><strong>CSS File:</strong> <?php echo file_exists(BOOKINN_PLUGIN_DIR . 'assets/css/bookinn-administration.css') ? 'Exists ✅' : 'Missing ❌'; ?></p>
        <p><strong>JS File:</strong> <?php echo file_exists(BOOKINN_PLUGIN_DIR . 'assets/js/bookinn-administration.js') ? 'Exists ✅' : 'Missing ❌'; ?></p>
        <p><strong>Plugin Version:</strong> <?php echo BOOKINN_VERSION; ?></p>
    </div>
</div>

<script>
// Debug script to test widget initialization
jQuery(document).ready(function($) {
    console.log('BookInn Administration Test: Page loaded');
    console.log('BookInn object:', window.BookInn);
    console.log('Administration object:', window.BookInn ? window.BookInn.Administration : 'Not found');
    
    // Test if widget initializes
    setTimeout(function() {
        if (window.BookInn && window.BookInn.Administration && window.BookInn.Administration.initialized) {
            console.log('✅ Administration widget initialized successfully');
        } else {
            console.log('❌ Administration widget failed to initialize');
        }
    }, 2000);
});
</script>

<?php get_footer(); ?>
