<?php
/**
 * BookInn Administration Widget
 * 
 * Complete administration interface for hotel management settings and OTA management
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Widget_Administration extends WP_Widget {
    
    /**
     * Constructor
     */
    public function __construct() {
        $widget_options = array(
            'classname' => 'bookinn-widget bookinn-administration-widget',
            'description' => __('Complete administration interface with frontend settings and OTA management.', 'bookinn'),
            'customize_selective_refresh' => true,
        );
        
        $control_options = array(
            'width' => 600,
            'height' => 400
        );
        
        parent::__construct(
            'bookinn_administration',
            __('BookInn - Administration', 'bookinn'),
            $widget_options,
            $control_options
        );
        
        // Enqueue assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'), 10);
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Initialize AJAX handlers
        $this->init_ajax_handlers();
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax_handlers() {
        // Frontend Settings AJAX handlers
        add_action('wp_ajax_bookinn_get_hotel_settings', array($this, 'ajax_get_hotel_settings'));
        add_action('wp_ajax_bookinn_save_hotel_settings', array($this, 'ajax_save_hotel_settings'));
        add_action('wp_ajax_bookinn_get_user_management', array($this, 'ajax_get_user_management'));
        add_action('wp_ajax_bookinn_save_user_settings', array($this, 'ajax_save_user_settings'));
        
        // OTA Management AJAX handlers
        add_action('wp_ajax_bookinn_get_ota_overview', array($this, 'ajax_get_ota_overview'));
        add_action('wp_ajax_bookinn_get_ota_channels', array($this, 'ajax_get_ota_channels'));
        add_action('wp_ajax_bookinn_save_room_mapping', array($this, 'ajax_save_room_mapping'));
        add_action('wp_ajax_bookinn_get_sync_status', array($this, 'ajax_get_sync_status'));
        add_action('wp_ajax_bookinn_get_activity_logs', array($this, 'ajax_get_activity_logs'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_assets() {
        if (!is_active_widget(false, false, $this->id_base)) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style('bookinn-administration-widget',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-administration.css',
            array('bookinn-management-unified'), BOOKINN_VERSION);
        
        // Enqueue JavaScript
        wp_enqueue_script('bookinn-administration-widget',
            BOOKINN_PLUGIN_URL . 'assets/js/bookinn-administration.js',
            array('jquery', 'bookinn-main', 'bookinn-management-unified'), BOOKINN_VERSION, true);
        
        // Localize script
        wp_localize_script('bookinn-administration-widget', 'bookinnAdmin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('bookinn_admin_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'bookinn'),
                'error' => __('An error occurred', 'bookinn'),
                'success' => __('Settings saved successfully', 'bookinn'),
                'confirm_delete' => __('Are you sure you want to delete this item?', 'bookinn'),
            )
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if ('widgets.php' !== $hook) {
            return;
        }
        
        wp_enqueue_style('bookinn-widget-admin',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-widget-admin.css',
            array(), BOOKINN_VERSION);
    }
    
    /**
     * Widget form in admin
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Administration', 'bookinn');
        $default_tab = !empty($instance['default_tab']) ? $instance['default_tab'] : 'frontend-settings';
        $show_frontend_settings = isset($instance['show_frontend_settings']) ? (bool) $instance['show_frontend_settings'] : true;
        $show_ota_management = isset($instance['show_ota_management']) ? (bool) $instance['show_ota_management'] : true;
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Title:', 'bookinn'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" 
                   value="<?php echo esc_attr($title); ?>">
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('default_tab')); ?>"><?php _e('Default Tab:', 'bookinn'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('default_tab')); ?>" 
                    name="<?php echo esc_attr($this->get_field_name('default_tab')); ?>">
                <option value="frontend-settings" <?php selected($default_tab, 'frontend-settings'); ?>><?php _e('Frontend Settings', 'bookinn'); ?></option>
                <option value="ota-management" <?php selected($default_tab, 'ota-management'); ?>><?php _e('OTA Management', 'bookinn'); ?></option>
            </select>
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_frontend_settings); ?> 
                   id="<?php echo esc_attr($this->get_field_id('show_frontend_settings')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('show_frontend_settings')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('show_frontend_settings')); ?>"><?php _e('Show Frontend Settings', 'bookinn'); ?></label>
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_ota_management); ?> 
                   id="<?php echo esc_attr($this->get_field_id('show_ota_management')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('show_ota_management')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('show_ota_management')); ?>"><?php _e('Show OTA Management', 'bookinn'); ?></label>
        </p>
        <?php
    }
    
    /**
     * Update widget settings
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['default_tab'] = (!empty($new_instance['default_tab'])) ? sanitize_text_field($new_instance['default_tab']) : 'frontend-settings';
        $instance['show_frontend_settings'] = !empty($new_instance['show_frontend_settings']);
        $instance['show_ota_management'] = !empty($new_instance['show_ota_management']);
        
        return $instance;
    }
    
    /**
     * Display the widget content
     */
    public function widget($args, $instance) {
        // Check user permissions
        if (!current_user_can('manage_options')) {
            echo '<div class="bookinn-error">' . __('You do not have permission to access this administration panel.', 'bookinn') . '</div>';
            return;
        }
        
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        $this->render_administration_interface($instance);
        
        echo $args['after_widget'];
    }
    
    /**
     * Render administration interface
     */
    private function render_administration_interface($instance) {
        $default_tab = $instance['default_tab'] ?? 'frontend-settings';
        ?>
        <div class="bookinn-administration-container" data-default-tab="<?php echo esc_attr($default_tab); ?>">
            <!-- Administration Header -->
            <div class="bookinn-administration-header">
                <div class="bookinn-header-content">
                    <img src="<?php echo BOOKINN_PLUGIN_URL . 'assets/images/logo_header_widget.png'; ?>" alt="BookInn Logo" class="bookinn-logo">
                    <h2 class="bookinn-administration-title"><?php _e('Administration Panel', 'bookinn'); ?></h2>
                    <div class="bookinn-header-actions">
                        <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-admin">
                            <i class="fa-solid fa-refresh bookinn-fa"></i>
                            <?php _e('Refresh', 'bookinn'); ?>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Tab Navigation -->
            <nav class="bookinn-administration-nav">
                <ul class="bookinn-tab-nav" role="tablist">
                    <?php if ($instance['show_frontend_settings'] ?? true): ?>
                    <li class="bookinn-tab-item">
                        <a href="#frontend-settings" class="bookinn-tab-link active" role="tab" data-tab="frontend-settings">
                            <span class="dashicons dashicons-admin-settings"></span>
                            <?php _e('Frontend Settings', 'bookinn'); ?>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($instance['show_ota_management'] ?? true): ?>
                    <li class="bookinn-tab-item">
                        <a href="#ota-management" class="bookinn-tab-link" role="tab" data-tab="ota-management">
                            <span class="dashicons dashicons-networking"></span>
                            <?php _e('OTA Management', 'bookinn'); ?>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <!-- Tab Content -->
            <div class="bookinn-administration-content">
                <?php if ($instance['show_frontend_settings'] ?? true): ?>
                <div id="frontend-settings" class="bookinn-tab-content active" role="tabpanel">
                    <?php $this->render_frontend_settings_tab(); ?>
                </div>
                <?php endif; ?>
                
                <?php if ($instance['show_ota_management'] ?? true): ?>
                <div id="ota-management" class="bookinn-tab-content" role="tabpanel">
                    <?php $this->render_ota_management_tab(); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render Frontend Settings Tab
     */
    private function render_frontend_settings_tab() {
        ?>
        <div class="bookinn-frontend-settings-container">
            <!-- Vertical Sub-tabs Navigation -->
            <div class="bookinn-vertical-tabs">
                <div class="bookinn-vertical-nav">
                    <a href="#hotel-management" class="bookinn-vertical-tab-link active" data-subtab="hotel-management">
                        <i class="fa-solid fa-building bookinn-fa"></i>
                        <?php _e('Hotel Management', 'bookinn'); ?>
                    </a>
                    <a href="#user-management" class="bookinn-vertical-tab-link" data-subtab="user-management">
                        <i class="fa-solid fa-users bookinn-fa"></i>
                        <?php _e('User Management', 'bookinn'); ?>
                    </a>
                </div>

                <!-- Vertical Sub-tabs Content -->
                <div class="bookinn-vertical-content">
                    <!-- Hotel Management Sub-tab -->
                    <div class="bookinn-vertical-panel active" id="hotel-management">
                        <?php $this->render_hotel_management_subtab(); ?>
                    </div>

                    <!-- User Management Sub-tab -->
                    <div class="bookinn-vertical-panel" id="user-management">
                        <?php $this->render_user_management_subtab(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render OTA Management Tab
     */
    private function render_ota_management_tab() {
        ?>
        <div class="bookinn-ota-management-container">
            <!-- Vertical Sub-tabs Navigation -->
            <div class="bookinn-vertical-tabs">
                <div class="bookinn-vertical-nav">
                    <a href="#ota-overview" class="bookinn-vertical-tab-link active" data-subtab="ota-overview">
                        <i class="fa-solid fa-dashboard bookinn-fa"></i>
                        <?php _e('Overview', 'bookinn'); ?>
                    </a>
                    <a href="#ota-channels" class="bookinn-vertical-tab-link" data-subtab="ota-channels">
                        <i class="fa-solid fa-broadcast-tower bookinn-fa"></i>
                        <?php _e('Channels', 'bookinn'); ?>
                    </a>
                    <a href="#room-mapping" class="bookinn-vertical-tab-link" data-subtab="room-mapping">
                        <i class="fa-solid fa-sitemap bookinn-fa"></i>
                        <?php _e('Room Mapping', 'bookinn'); ?>
                    </a>
                    <a href="#sync-status" class="bookinn-vertical-tab-link" data-subtab="sync-status">
                        <i class="fa-solid fa-sync bookinn-fa"></i>
                        <?php _e('Sync Status', 'bookinn'); ?>
                    </a>
                    <a href="#activity-logs" class="bookinn-vertical-tab-link" data-subtab="activity-logs">
                        <i class="fa-solid fa-list bookinn-fa"></i>
                        <?php _e('Activity Logs', 'bookinn'); ?>
                    </a>
                </div>

                <!-- Vertical Sub-tabs Content -->
                <div class="bookinn-vertical-content">
                    <!-- OTA Overview Sub-tab -->
                    <div class="bookinn-vertical-panel active" id="ota-overview">
                        <?php $this->render_ota_overview_subtab(); ?>
                    </div>

                    <!-- OTA Channels Sub-tab -->
                    <div class="bookinn-vertical-panel" id="ota-channels">
                        <?php $this->render_ota_channels_subtab(); ?>
                    </div>

                    <!-- Room Mapping Sub-tab -->
                    <div class="bookinn-vertical-panel" id="room-mapping">
                        <?php $this->render_room_mapping_subtab(); ?>
                    </div>

                    <!-- Sync Status Sub-tab -->
                    <div class="bookinn-vertical-panel" id="sync-status">
                        <?php $this->render_sync_status_subtab(); ?>
                    </div>

                    <!-- Activity Logs Sub-tab -->
                    <div class="bookinn-vertical-panel" id="activity-logs">
                        <?php $this->render_activity_logs_subtab(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render Hotel Management Sub-tab
     */
    private function render_hotel_management_subtab() {
        ?>
        <div class="bookinn-hotel-management-content">
            <div class="bookinn-section-header">
                <h3><?php _e('Hotel Settings', 'bookinn'); ?></h3>
                <button class="bookinn-btn bookinn-btn-primary" id="save-hotel-settings">
                    <i class="fa-solid fa-save bookinn-fa"></i>
                    <?php _e('Save Settings', 'bookinn'); ?>
                </button>
            </div>

            <div class="bookinn-loading-placeholder" id="hotel-settings-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading hotel settings...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="hotel-settings-content" style="display: none;">
                <!-- Hotel settings form will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    /**
     * Render User Management Sub-tab
     */
    private function render_user_management_subtab() {
        ?>
        <div class="bookinn-user-management-content">
            <div class="bookinn-section-header">
                <h3><?php _e('User Management', 'bookinn'); ?></h3>
                <button class="bookinn-btn bookinn-btn-primary" id="add-new-user">
                    <i class="fa-solid fa-plus bookinn-fa"></i>
                    <?php _e('Add User', 'bookinn'); ?>
                </button>
            </div>

            <div class="bookinn-loading-placeholder" id="user-management-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading user management...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="user-management-content" style="display: none;">
                <!-- User management content will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    /**
     * Render OTA Overview Sub-tab
     */
    private function render_ota_overview_subtab() {
        ?>
        <div class="bookinn-ota-overview-content">
            <div class="bookinn-section-header">
                <h3><?php _e('OTA Overview', 'bookinn'); ?></h3>
                <button class="bookinn-btn bookinn-btn-secondary" id="refresh-ota-overview">
                    <i class="fa-solid fa-refresh bookinn-fa"></i>
                    <?php _e('Refresh', 'bookinn'); ?>
                </button>
            </div>

            <div class="bookinn-loading-placeholder" id="ota-overview-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading OTA overview...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="ota-overview-content" style="display: none;">
                <!-- OTA overview content will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    /**
     * Render OTA Channels Sub-tab
     */
    private function render_ota_channels_subtab() {
        ?>
        <div class="bookinn-ota-channels-content">
            <div class="bookinn-section-header">
                <h3><?php _e('OTA Channels', 'bookinn'); ?></h3>
                <button class="bookinn-btn bookinn-btn-primary" id="add-ota-channel">
                    <i class="fa-solid fa-plus bookinn-fa"></i>
                    <?php _e('Add Channel', 'bookinn'); ?>
                </button>
            </div>

            <div class="bookinn-loading-placeholder" id="ota-channels-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading OTA channels...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="ota-channels-content" style="display: none;">
                <!-- OTA channels content will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    /**
     * Render Room Mapping Sub-tab
     */
    private function render_room_mapping_subtab() {
        ?>
        <div class="bookinn-room-mapping-content">
            <div class="bookinn-section-header">
                <h3><?php _e('Room Mapping', 'bookinn'); ?></h3>
                <button class="bookinn-btn bookinn-btn-primary" id="save-room-mapping">
                    <i class="fa-solid fa-save bookinn-fa"></i>
                    <?php _e('Save Mapping', 'bookinn'); ?>
                </button>
            </div>

            <div class="bookinn-loading-placeholder" id="room-mapping-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading room mapping...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="room-mapping-content" style="display: none;">
                <!-- Room mapping content will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    /**
     * Render Sync Status Sub-tab
     */
    private function render_sync_status_subtab() {
        ?>
        <div class="bookinn-sync-status-content">
            <div class="bookinn-section-header">
                <h3><?php _e('Sync Status', 'bookinn'); ?></h3>
                <button class="bookinn-btn bookinn-btn-secondary" id="refresh-sync-status">
                    <i class="fa-solid fa-refresh bookinn-fa"></i>
                    <?php _e('Refresh', 'bookinn'); ?>
                </button>
            </div>

            <div class="bookinn-loading-placeholder" id="sync-status-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading sync status...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="sync-status-content" style="display: none;">
                <!-- Sync status content will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    /**
     * Render Activity Logs Sub-tab
     */
    private function render_activity_logs_subtab() {
        ?>
        <div class="bookinn-activity-logs-content">
            <div class="bookinn-section-header">
                <h3><?php _e('Activity Logs', 'bookinn'); ?></h3>
                <div class="bookinn-header-actions">
                    <button class="bookinn-btn bookinn-btn-secondary" id="clear-activity-logs">
                        <i class="fa-solid fa-trash bookinn-fa"></i>
                        <?php _e('Clear Logs', 'bookinn'); ?>
                    </button>
                    <button class="bookinn-btn bookinn-btn-secondary" id="refresh-activity-logs">
                        <i class="fa-solid fa-refresh bookinn-fa"></i>
                        <?php _e('Refresh', 'bookinn'); ?>
                    </button>
                </div>
            </div>

            <div class="bookinn-loading-placeholder" id="activity-logs-loading">
                <div class="bookinn-spinner"></div>
                <p><?php _e('Loading activity logs...', 'bookinn'); ?></p>
            </div>

            <div class="bookinn-content-area" id="activity-logs-content" style="display: none;">
                <!-- Activity logs content will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }

    // ===== AJAX HANDLERS =====

    /**
     * AJAX: Get hotel settings
     */
    public function ajax_get_hotel_settings() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        global $wpdb;
        $hotels_table = $wpdb->prefix . 'bookinn_hotels';

        // Get hotel data
        $hotel = $wpdb->get_row("SELECT * FROM {$hotels_table} LIMIT 1");

        if (!$hotel) {
            // Create default hotel entry
            $wpdb->insert($hotels_table, array(
                'name' => get_bloginfo('name'),
                'description' => '',
                'address' => '',
                'phone' => '',
                'email' => get_option('admin_email'),
                'website' => home_url(),
                'check_in_time' => '14:00:00',
                'check_out_time' => '11:00:00',
                'currency' => 'EUR',
                'tax_rate' => 0.00
            ));

            $hotel = $wpdb->get_row("SELECT * FROM {$hotels_table} LIMIT 1");
        }

        ob_start();
        ?>
        <form id="hotel-settings-form" class="bookinn-form">
            <div class="bookinn-form-grid">
                <div class="bookinn-form-group">
                    <label for="hotel-name"><?php _e('Hotel Name', 'bookinn'); ?></label>
                    <input type="text" id="hotel-name" name="name" value="<?php echo esc_attr($hotel->name); ?>" class="bookinn-input" required>
                </div>

                <div class="bookinn-form-group">
                    <label for="hotel-email"><?php _e('Email', 'bookinn'); ?></label>
                    <input type="email" id="hotel-email" name="email" value="<?php echo esc_attr($hotel->email); ?>" class="bookinn-input" required>
                </div>

                <div class="bookinn-form-group">
                    <label for="hotel-phone"><?php _e('Phone', 'bookinn'); ?></label>
                    <input type="tel" id="hotel-phone" name="phone" value="<?php echo esc_attr($hotel->phone); ?>" class="bookinn-input">
                </div>

                <div class="bookinn-form-group">
                    <label for="hotel-website"><?php _e('Website', 'bookinn'); ?></label>
                    <input type="url" id="hotel-website" name="website" value="<?php echo esc_attr($hotel->website); ?>" class="bookinn-input">
                </div>

                <div class="bookinn-form-group bookinn-form-group-full">
                    <label for="hotel-address"><?php _e('Address', 'bookinn'); ?></label>
                    <textarea id="hotel-address" name="address" class="bookinn-textarea" rows="3"><?php echo esc_textarea($hotel->address); ?></textarea>
                </div>

                <div class="bookinn-form-group bookinn-form-group-full">
                    <label for="hotel-description"><?php _e('Description', 'bookinn'); ?></label>
                    <textarea id="hotel-description" name="description" class="bookinn-textarea" rows="4"><?php echo esc_textarea($hotel->description); ?></textarea>
                </div>

                <div class="bookinn-form-group">
                    <label for="check-in-time"><?php _e('Check-in Time', 'bookinn'); ?></label>
                    <input type="time" id="check-in-time" name="check_in_time" value="<?php echo esc_attr($hotel->check_in_time); ?>" class="bookinn-input">
                </div>

                <div class="bookinn-form-group">
                    <label for="check-out-time"><?php _e('Check-out Time', 'bookinn'); ?></label>
                    <input type="time" id="check-out-time" name="check_out_time" value="<?php echo esc_attr($hotel->check_out_time); ?>" class="bookinn-input">
                </div>

                <div class="bookinn-form-group">
                    <label for="currency"><?php _e('Currency', 'bookinn'); ?></label>
                    <select id="currency" name="currency" class="bookinn-select">
                        <option value="EUR" <?php selected($hotel->currency, 'EUR'); ?>>EUR (€)</option>
                        <option value="USD" <?php selected($hotel->currency, 'USD'); ?>>USD ($)</option>
                        <option value="GBP" <?php selected($hotel->currency, 'GBP'); ?>>GBP (£)</option>
                    </select>
                </div>

                <div class="bookinn-form-group">
                    <label for="tax-rate"><?php _e('Tax Rate (%)', 'bookinn'); ?></label>
                    <input type="number" id="tax-rate" name="tax_rate" value="<?php echo esc_attr($hotel->tax_rate); ?>" class="bookinn-input" min="0" max="100" step="0.01">
                </div>
            </div>
        </form>
        <?php

        wp_send_json_success(array(
            'html' => ob_get_clean()
        ));
    }

    /**
     * AJAX: Save hotel settings
     */
    public function ajax_save_hotel_settings() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        global $wpdb;
        $hotels_table = $wpdb->prefix . 'bookinn_hotels';

        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'address' => sanitize_textarea_field($_POST['address']),
            'phone' => sanitize_text_field($_POST['phone']),
            'email' => sanitize_email($_POST['email']),
            'website' => esc_url_raw($_POST['website']),
            'check_in_time' => sanitize_text_field($_POST['check_in_time']),
            'check_out_time' => sanitize_text_field($_POST['check_out_time']),
            'currency' => sanitize_text_field($_POST['currency']),
            'tax_rate' => floatval($_POST['tax_rate'])
        );

        $hotel_exists = $wpdb->get_var("SELECT COUNT(*) FROM {$hotels_table}");

        if ($hotel_exists) {
            $result = $wpdb->update($hotels_table, $data, array('id' => 1));
        } else {
            $result = $wpdb->insert($hotels_table, $data);
        }

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => __('Hotel settings saved successfully', 'bookinn')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to save hotel settings', 'bookinn')
            ));
        }
    }

    /**
     * AJAX: Get user management
     */
    public function ajax_get_user_management() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        $users = get_users(array(
            'meta_key' => 'bookinn_role',
            'meta_value' => array('hotel_manager', 'receptionist'),
            'meta_compare' => 'IN'
        ));

        ob_start();
        ?>
        <div class="bookinn-user-list">
            <div class="bookinn-table-container">
                <table class="bookinn-table">
                    <thead>
                        <tr>
                            <th><?php _e('Name', 'bookinn'); ?></th>
                            <th><?php _e('Email', 'bookinn'); ?></th>
                            <th><?php _e('Role', 'bookinn'); ?></th>
                            <th><?php _e('Status', 'bookinn'); ?></th>
                            <th><?php _e('Actions', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="5" class="bookinn-no-data">
                                <?php _e('No users found', 'bookinn'); ?>
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo esc_html($user->display_name); ?></td>
                            <td><?php echo esc_html($user->user_email); ?></td>
                            <td>
                                <span class="bookinn-badge bookinn-badge-info">
                                    <?php echo esc_html(get_user_meta($user->ID, 'bookinn_role', true)); ?>
                                </span>
                            </td>
                            <td>
                                <span class="bookinn-badge bookinn-badge-success">
                                    <?php _e('Active', 'bookinn'); ?>
                                </span>
                            </td>
                            <td>
                                <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary edit-user" data-user-id="<?php echo $user->ID; ?>">
                                    <i class="fa-solid fa-edit bookinn-fa"></i>
                                    <?php _e('Edit', 'bookinn'); ?>
                                </button>
                                <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger delete-user" data-user-id="<?php echo $user->ID; ?>">
                                    <i class="fa-solid fa-trash bookinn-fa"></i>
                                    <?php _e('Delete', 'bookinn'); ?>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php

        wp_send_json_success(array(
            'html' => ob_get_clean()
        ));
    }

    /**
     * AJAX: Save user settings
     */
    public function ajax_save_user_settings() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        $user_id = intval($_POST['user_id']);
        $bookinn_role = sanitize_text_field($_POST['bookinn_role']);

        if ($user_id && in_array($bookinn_role, array('hotel_manager', 'receptionist'))) {
            update_user_meta($user_id, 'bookinn_role', $bookinn_role);

            wp_send_json_success(array(
                'message' => __('User settings saved successfully', 'bookinn')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Invalid user data', 'bookinn')
            ));
        }
    }

    /**
     * AJAX: Get OTA overview
     */
    public function ajax_get_ota_overview() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        // Mock OTA data for demonstration
        $ota_stats = array(
            'total_channels' => 3,
            'active_channels' => 2,
            'sync_errors' => 1,
            'last_sync' => current_time('mysql')
        );

        ob_start();
        ?>
        <div class="bookinn-ota-overview">
            <div class="bookinn-metrics-grid">
                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-broadcast-tower bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($ota_stats['total_channels']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Total Channels', 'bookinn'); ?></p>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-check-circle bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($ota_stats['active_channels']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Active Channels', 'bookinn'); ?></p>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-exclamation-triangle bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($ota_stats['sync_errors']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Sync Errors', 'bookinn'); ?></p>
                    </div>
                </div>
            </div>

            <div class="bookinn-recent-activity">
                <h4><?php _e('Recent Activity', 'bookinn'); ?></h4>
                <div class="bookinn-activity-list">
                    <div class="bookinn-activity-item">
                        <div class="bookinn-activity-icon">
                            <i class="fa-solid fa-sync bookinn-fa"></i>
                        </div>
                        <div class="bookinn-activity-content">
                            <p><?php _e('Last sync completed', 'bookinn'); ?></p>
                            <span class="bookinn-activity-time"><?php echo esc_html($ota_stats['last_sync']); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php

        wp_send_json_success(array(
            'html' => ob_get_clean()
        ));
    }

    /**
     * AJAX: Get OTA channels
     */
    public function ajax_get_ota_channels() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        // Mock channels data
        $channels = array(
            array('id' => 1, 'name' => 'Booking.com', 'status' => 'active', 'last_sync' => '2024-01-15 10:30:00'),
            array('id' => 2, 'name' => 'Expedia', 'status' => 'active', 'last_sync' => '2024-01-15 10:25:00'),
            array('id' => 3, 'name' => 'Airbnb', 'status' => 'inactive', 'last_sync' => '2024-01-14 15:20:00')
        );

        ob_start();
        ?>
        <div class="bookinn-channels-list">
            <div class="bookinn-table-container">
                <table class="bookinn-table">
                    <thead>
                        <tr>
                            <th><?php _e('Channel', 'bookinn'); ?></th>
                            <th><?php _e('Status', 'bookinn'); ?></th>
                            <th><?php _e('Last Sync', 'bookinn'); ?></th>
                            <th><?php _e('Actions', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($channels as $channel): ?>
                        <tr>
                            <td><?php echo esc_html($channel['name']); ?></td>
                            <td>
                                <span class="bookinn-badge <?php echo $channel['status'] === 'active' ? 'bookinn-badge-success' : 'bookinn-badge-warning'; ?>">
                                    <?php echo esc_html(ucfirst($channel['status'])); ?>
                                </span>
                            </td>
                            <td><?php echo esc_html($channel['last_sync']); ?></td>
                            <td>
                                <button class="bookinn-btn bookinn-btn-sm bookinn-btn-primary sync-channel" data-channel-id="<?php echo $channel['id']; ?>">
                                    <i class="fa-solid fa-sync bookinn-fa"></i>
                                    <?php _e('Sync', 'bookinn'); ?>
                                </button>
                                <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary edit-channel" data-channel-id="<?php echo $channel['id']; ?>">
                                    <i class="fa-solid fa-edit bookinn-fa"></i>
                                    <?php _e('Edit', 'bookinn'); ?>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php

        wp_send_json_success(array(
            'html' => ob_get_clean()
        ));
    }

    /**
     * AJAX: Save room mapping
     */
    public function ajax_save_room_mapping() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        $mapping_data = $_POST['mapping_data'];

        // Save mapping data (implement actual logic here)
        update_option('bookinn_room_mapping', $mapping_data);

        wp_send_json_success(array(
            'message' => __('Room mapping saved successfully', 'bookinn')
        ));
    }

    /**
     * AJAX: Get sync status
     */
    public function ajax_get_sync_status() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        // Mock sync status data
        ob_start();
        ?>
        <div class="bookinn-sync-status">
            <div class="bookinn-sync-item">
                <div class="bookinn-sync-channel">Booking.com</div>
                <div class="bookinn-sync-progress">
                    <div class="bookinn-progress-bar">
                        <div class="bookinn-progress-fill" style="width: 100%"></div>
                    </div>
                    <span class="bookinn-sync-text"><?php _e('Completed', 'bookinn'); ?></span>
                </div>
            </div>

            <div class="bookinn-sync-item">
                <div class="bookinn-sync-channel">Expedia</div>
                <div class="bookinn-sync-progress">
                    <div class="bookinn-progress-bar">
                        <div class="bookinn-progress-fill" style="width: 75%"></div>
                    </div>
                    <span class="bookinn-sync-text"><?php _e('In Progress', 'bookinn'); ?></span>
                </div>
            </div>
        </div>
        <?php

        wp_send_json_success(array(
            'html' => ob_get_clean()
        ));
    }

    /**
     * AJAX: Get activity logs
     */
    public function ajax_get_activity_logs() {
        check_ajax_referer('bookinn_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'bookinn'));
        }

        // Mock activity logs
        $logs = array(
            array('time' => '2024-01-15 10:30:00', 'action' => 'Sync completed', 'channel' => 'Booking.com', 'status' => 'success'),
            array('time' => '2024-01-15 10:25:00', 'action' => 'Room mapping updated', 'channel' => 'Expedia', 'status' => 'success'),
            array('time' => '2024-01-15 09:15:00', 'action' => 'Sync failed', 'channel' => 'Airbnb', 'status' => 'error')
        );

        ob_start();
        ?>
        <div class="bookinn-activity-logs">
            <div class="bookinn-table-container">
                <table class="bookinn-table">
                    <thead>
                        <tr>
                            <th><?php _e('Time', 'bookinn'); ?></th>
                            <th><?php _e('Action', 'bookinn'); ?></th>
                            <th><?php _e('Channel', 'bookinn'); ?></th>
                            <th><?php _e('Status', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                        <tr>
                            <td><?php echo esc_html($log['time']); ?></td>
                            <td><?php echo esc_html($log['action']); ?></td>
                            <td><?php echo esc_html($log['channel']); ?></td>
                            <td>
                                <span class="bookinn-badge <?php echo $log['status'] === 'success' ? 'bookinn-badge-success' : 'bookinn-badge-danger'; ?>">
                                    <?php echo esc_html(ucfirst($log['status'])); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php

        wp_send_json_success(array(
            'html' => ob_get_clean()
        ));
    }
}
