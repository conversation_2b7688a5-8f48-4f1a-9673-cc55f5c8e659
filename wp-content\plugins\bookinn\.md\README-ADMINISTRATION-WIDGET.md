# BookInn Administration Widget

## Overview

The BookInn Administration Widget provides a comprehensive administration interface for hotel management settings and OTA (Online Travel Agency) management. It follows the exact same styling patterns and structure as the existing Management Center widget, ensuring visual and functional consistency across the BookInn plugin ecosystem.

## Features

### 1. Frontend Settings
- **Hotel Management**: Complete hotel configuration including basic information, contact details, check-in/out times, currency, and tax settings
- **User Management**: Manage hotel staff users with different roles (hotel manager, receptionist)

### 2. OTA Management
- **Overview**: Dashboard showing OTA statistics, active channels, sync errors, and recent activity
- **Channels**: Manage OTA channel connections (Booking.com, Expedia, Airbnb, etc.)
- **Room Mapping**: Map local rooms to OTA channel room types
- **Sync Status**: Real-time synchronization status with progress indicators
- **Activity Logs**: Comprehensive logging of all OTA-related activities

## File Structure

```
wp-content/plugins/bookinn/
├── includes/widgets/
│   └── class-bookinn-widget-administration.php    # Main widget class
├── assets/css/
│   └── bookinn-administration.css                 # Widget styles
├── assets/js/
│   └── bookinn-administration.js                  # Widget JavaScript
└── .md/
    └── README-ADMINISTRATION-WIDGET.md            # This documentation
```

## Implementation Details

### Widget Class Structure
- **Extends**: `WP_Widget`
- **Namespace**: `BookInn_Widget_Administration`
- **Widget ID**: `bookinn_administration`
- **CSS Classes**: `bookinn-widget bookinn-administration-widget`

### Navigation Structure
- **Main Tabs**: Frontend Settings, OTA Management
- **Vertical Sub-tabs**: Each main tab contains vertical navigation for sub-sections
- **Responsive Design**: Adapts to mobile devices with collapsible navigation

### AJAX Handlers
All data loading and saving operations use AJAX for seamless user experience:

#### Frontend Settings
- `bookinn_get_hotel_settings` - Load hotel configuration form
- `bookinn_save_hotel_settings` - Save hotel settings
- `bookinn_get_user_management` - Load user management interface
- `bookinn_save_user_settings` - Save user role assignments

#### OTA Management
- `bookinn_get_ota_overview` - Load OTA dashboard metrics
- `bookinn_get_ota_channels` - Load channel management interface
- `bookinn_save_room_mapping` - Save room mapping configurations
- `bookinn_get_sync_status` - Load synchronization status
- `bookinn_get_activity_logs` - Load activity logs

### CSS Architecture
The widget follows the established BookInn design system:

- **CSS Variables**: Uses the same CSS custom properties as other widgets
- **Component Classes**: Consistent `.bookinn-*` naming convention
- **Layout System**: Flexbox and CSS Grid for responsive layouts
- **Color Scheme**: Matches the existing BookInn brand colors
- **Typography**: 12px legends, smaller labels, compact design

### JavaScript Architecture
- **Namespace**: `window.BookInn.Administration`
- **Initialization**: Auto-detects widget presence using MutationObserver
- **Event Handling**: Delegated event handlers for dynamic content
- **AJAX Integration**: Unified error handling and loading states
- **State Management**: Tracks current tab and subtab states

## Usage

### Adding the Widget
1. Go to WordPress Admin → Appearance → Widgets
2. Find "BookInn - Administration" widget
3. Drag to desired widget area
4. Configure widget settings:
   - **Title**: Custom widget title
   - **Default Tab**: Choose starting tab (Frontend Settings or OTA Management)
   - **Show Frontend Settings**: Toggle frontend settings section
   - **Show OTA Management**: Toggle OTA management section

### Widget Configuration Options
- **Title**: Customizable widget title
- **Default Tab**: Set which tab opens by default
- **Section Toggles**: Enable/disable specific sections
- **Permissions**: Requires `manage_options` capability

## Styling Consistency

The Administration widget maintains perfect consistency with existing BookInn widgets:

### Button Styles
- **Primary**: `.bookinn-btn-primary` - Blue gradient buttons for main actions
- **Secondary**: `.bookinn-btn-secondary` - White buttons with borders
- **Small**: `.bookinn-btn-sm` - Compact buttons for table actions

### Modal Patterns
- **Width**: Consistent modal widths (compact: 500px, wide: 900px)
- **Padding**: Standardized spacing using CSS variables
- **Animation**: Fade-in animations matching other widgets

### Form Components
- **Grid Layout**: Responsive form grids
- **Input Styling**: Consistent input, select, and textarea styles
- **Validation**: Visual feedback for form validation

### Table Components
- **Responsive Tables**: Horizontal scroll on mobile
- **Row Hover**: Subtle hover effects
- **Status Badges**: Color-coded status indicators

## Database Integration

### Hotel Settings
Stores hotel configuration in `wp_bookinn_hotels` table:
- Basic information (name, description, address)
- Contact details (phone, email, website)
- Operational settings (check-in/out times, currency, tax rate)

### User Management
Uses WordPress user meta for role assignments:
- `bookinn_role` meta key for hotel staff roles
- Supported roles: `hotel_manager`, `receptionist`

### OTA Data
Mock data implementation ready for real OTA integration:
- Channel configurations
- Room mappings
- Sync status tracking
- Activity logging

## Security

### Permissions
- **Admin Access**: Requires `manage_options` capability
- **AJAX Security**: All AJAX requests use WordPress nonces
- **Data Sanitization**: All input data is properly sanitized

### Nonce Verification
- **Nonce Key**: `bookinn_admin_nonce`
- **AJAX Actions**: All AJAX handlers verify nonces
- **Form Submissions**: Protected against CSRF attacks

## Responsive Design

### Mobile Adaptations
- **Vertical Tabs**: Convert to horizontal scrolling on mobile
- **Form Grids**: Single column layout on small screens
- **Header Actions**: Stack vertically on mobile
- **Table Scrolling**: Horizontal scroll for wide tables

### Breakpoints
- **Desktop**: Full layout with vertical tabs
- **Tablet**: Adjusted spacing and font sizes
- **Mobile**: Stacked layout with horizontal navigation

## Integration Points

### With Existing Widgets
- **Shared CSS**: Uses `bookinn-management-unified.css` as base
- **JavaScript Dependencies**: Requires `bookinn-main.js` and `bookinn-management-unified.js`
- **Modal System**: Integrates with global BookInn modal system

### With WordPress
- **Widget API**: Standard WordPress widget implementation
- **AJAX API**: Uses WordPress AJAX system
- **User System**: Integrates with WordPress user management
- **Permissions**: Uses WordPress capability system

## Future Enhancements

### Planned Features
- **Real OTA Integration**: Connect to actual OTA APIs
- **Advanced User Roles**: More granular permission system
- **Audit Trail**: Enhanced activity logging
- **Bulk Operations**: Mass updates for channels and mappings
- **Export/Import**: Configuration backup and restore

### Extension Points
- **Custom Tabs**: Plugin architecture for additional tabs
- **Hook System**: WordPress actions and filters for customization
- **API Endpoints**: REST API for external integrations

## Troubleshooting

### Common Issues
1. **Widget Not Loading**: Check if CSS/JS files are properly enqueued
2. **AJAX Errors**: Verify nonce configuration and user permissions
3. **Styling Issues**: Ensure `bookinn-management-unified.css` is loaded first
4. **Mobile Layout**: Check responsive CSS media queries

### Debug Mode
Enable WordPress debug mode to see console logs:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Conclusion

The BookInn Administration Widget successfully extends the BookInn plugin ecosystem with a comprehensive administration interface that maintains perfect consistency with existing widgets while providing powerful new functionality for hotel management and OTA integration.
