/*
 * BookInn Administration Widget Styles
 * Following the exact same patterns as Management Center widget
 */

/* Import base variables from unified CSS */
@import url('bookinn-management-unified.css');

/* ===== ADMINISTRATION WIDGET CONTAINER ===== */
.bookinn-administration-container {
    font-family: var(--bookinn-font-family) !important;
    font-size: var(--bookinn-text-base) !important;
    line-height: 1.5 !important;
    color: var(--bookinn-text-primary) !important;
    background-color: var(--bookinn-bg-primary) !important;
    border-radius: var(--bookinn-radius-lg) !important;
    box-shadow: var(--bookinn-shadow-md) !important;
    overflow: hidden !important;
    min-height: 600px !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.bookinn-administration-container * {
    box-sizing: border-box;
}

/* ===== ADMINISTRATION HEADER ===== */
.bookinn-administration-header {
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    color: var(--bookinn-text-white);
    padding: var(--bookinn-space-4);
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--bookinn-space-3);
}

.bookinn-logo {
    height: 32px;
    width: auto;
}

.bookinn-administration-title {
    margin: 0;
    font-size: var(--bookinn-text-xl);
    font-weight: 600;
    color: var(--bookinn-text-white);
}

.bookinn-header-actions {
    display: flex;
    gap: var(--bookinn-space-2);
}

/* ===== TAB NAVIGATION ===== */
.bookinn-administration-nav {
    background: var(--bookinn-bg-secondary);
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-tab-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    overflow-x: auto;
}

.bookinn-tab-item {
    flex-shrink: 0;
}

.bookinn-tab-link {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2);
    padding: var(--bookinn-space-3) var(--bookinn-space-4);
    color: var(--bookinn-text-secondary);
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: var(--bookinn-transition);
    font-weight: 500;
    font-size: var(--bookinn-text-sm);
}

.bookinn-tab-link:hover {
    color: var(--bookinn-primary);
    background-color: var(--bookinn-bg-primary);
}

.bookinn-tab-link.active {
    color: var(--bookinn-primary);
    border-bottom-color: var(--bookinn-primary);
    background-color: var(--bookinn-bg-primary);
}

.bookinn-tab-link .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* ===== TAB CONTENT ===== */
.bookinn-administration-content {
    position: relative;
    min-height: 500px;
}

.bookinn-tab-content {
    display: none;
    padding: var(--bookinn-space-6);
    animation: fadeIn 0.3s ease-in-out;
}

.bookinn-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== VERTICAL TABS LAYOUT ===== */
.bookinn-vertical-tabs {
    display: flex;
    gap: var(--bookinn-space-6);
    min-height: 400px;
}

.bookinn-vertical-nav {
    flex: 0 0 200px;
    background: var(--bookinn-bg-secondary);
    border-radius: var(--bookinn-radius);
    padding: var(--bookinn-space-3);
    border: 1px solid var(--bookinn-border-light);
}

.bookinn-vertical-tab-link {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2);
    padding: var(--bookinn-space-3);
    color: var(--bookinn-text-secondary);
    text-decoration: none;
    border-radius: var(--bookinn-radius);
    transition: var(--bookinn-transition);
    font-weight: 500;
    font-size: var(--bookinn-text-sm);
    margin-bottom: var(--bookinn-space-1);
}

.bookinn-vertical-tab-link:hover {
    color: var(--bookinn-primary);
    background-color: var(--bookinn-bg-primary);
}

.bookinn-vertical-tab-link.active {
    color: var(--bookinn-text-white);
    background-color: var(--bookinn-primary);
}

.bookinn-vertical-content {
    flex: 1;
    background: var(--bookinn-bg-primary);
    border-radius: var(--bookinn-radius);
    border: 1px solid var(--bookinn-border-light);
    overflow: hidden;
}

.bookinn-vertical-panel {
    display: none;
    padding: var(--bookinn-space-6);
    height: 100%;
}

.bookinn-vertical-panel.active {
    display: block;
}

/* ===== SECTION HEADERS ===== */
.bookinn-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--bookinn-space-6);
    padding-bottom: var(--bookinn-space-3);
    border-bottom: 2px solid var(--bookinn-border-light);
}

.bookinn-section-header h3 {
    margin: 0;
    font-size: var(--bookinn-text-lg);
    font-weight: 600;
    color: var(--bookinn-text-primary);
}

.bookinn-header-actions {
    display: flex;
    gap: var(--bookinn-space-2);
}

/* ===== LOADING STATES ===== */
.bookinn-loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--bookinn-space-8);
    color: var(--bookinn-text-secondary);
}

.bookinn-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--bookinn-border-light);
    border-top: 3px solid var(--bookinn-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--bookinn-space-3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== CONTENT AREAS ===== */
.bookinn-content-area {
    animation: fadeIn 0.3s ease-in-out;
}

/* ===== FORM STYLES ===== */
.bookinn-form {
    max-width: 800px;
}

.bookinn-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--bookinn-space-4);
}

.bookinn-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--bookinn-space-1);
}

.bookinn-form-group-full {
    grid-column: 1 / -1;
}

.bookinn-form-group label {
    font-weight: 500;
    color: var(--bookinn-text-primary);
    font-size: var(--bookinn-text-sm);
}

.bookinn-input,
.bookinn-select,
.bookinn-textarea {
    padding: var(--bookinn-space-2);
    border: 1px solid var(--bookinn-border-medium);
    border-radius: var(--bookinn-radius);
    font-size: var(--bookinn-text-sm);
    transition: var(--bookinn-transition);
    background-color: var(--bookinn-bg-primary);
    color: var(--bookinn-text-primary);
}

.bookinn-input:focus,
.bookinn-select:focus,
.bookinn-textarea:focus {
    outline: none;
    border-color: var(--bookinn-primary);
    box-shadow: 0 0 0 3px rgba(39, 70, 144, 0.1);
}

.bookinn-textarea {
    resize: vertical;
    min-height: 80px;
}

/* ===== TABLE STYLES ===== */
.bookinn-table-container {
    overflow-x: auto;
    border: 1px solid var(--bookinn-border-light);
    border-radius: var(--bookinn-radius);
}

.bookinn-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--bookinn-text-sm);
}

.bookinn-table th,
.bookinn-table td {
    padding: var(--bookinn-space-3);
    text-align: left;
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-table th {
    background-color: var(--bookinn-bg-secondary);
    font-weight: 600;
    color: var(--bookinn-text-primary);
}

.bookinn-table tbody tr:hover {
    background-color: var(--bookinn-bg-secondary);
}

.bookinn-no-data {
    text-align: center;
    color: var(--bookinn-text-secondary);
    font-style: italic;
}

/* ===== BADGES ===== */
.bookinn-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--bookinn-space-1) var(--bookinn-space-2);
    font-size: 11px;
    font-weight: 500;
    border-radius: var(--bookinn-radius);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookinn-badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.bookinn-badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.bookinn-badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.bookinn-badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* ===== METRICS GRID ===== */
.bookinn-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--bookinn-space-4);
    margin-bottom: var(--bookinn-space-6);
}

.bookinn-metric-card {
    background: var(--bookinn-bg-primary);
    border: 1px solid var(--bookinn-border-light);
    border-radius: var(--bookinn-radius-lg);
    padding: var(--bookinn-space-4);
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-3);
    transition: var(--bookinn-transition);
}

.bookinn-metric-card:hover {
    box-shadow: var(--bookinn-shadow-md);
    transform: translateY(-2px);
}

.bookinn-metric-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: var(--bookinn-radius);
    background: linear-gradient(135deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bookinn-text-white);
}

.bookinn-metric-icon .bookinn-fa {
    font-size: 20px;
}

.bookinn-metric-content {
    flex: 1;
}

.bookinn-metric-value {
    font-size: var(--bookinn-text-2xl);
    font-weight: 700;
    color: var(--bookinn-text-primary);
    margin: 0 0 var(--bookinn-space-1) 0;
    line-height: 1;
}

.bookinn-metric-label {
    font-size: var(--bookinn-text-sm);
    color: var(--bookinn-text-secondary);
    margin: 0;
    font-weight: 500;
}

/* ===== ACTIVITY STYLES ===== */
.bookinn-recent-activity {
    margin-top: var(--bookinn-space-6);
}

.bookinn-recent-activity h4 {
    margin: 0 0 var(--bookinn-space-4) 0;
    font-size: var(--bookinn-text-lg);
    font-weight: 600;
    color: var(--bookinn-text-primary);
}

.bookinn-activity-list {
    background: var(--bookinn-bg-primary);
    border: 1px solid var(--bookinn-border-light);
    border-radius: var(--bookinn-radius);
    overflow: hidden;
}

.bookinn-activity-item {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-3);
    padding: var(--bookinn-space-4);
    border-bottom: 1px solid var(--bookinn-border-light);
}

.bookinn-activity-item:last-child {
    border-bottom: none;
}

.bookinn-activity-icon {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--bookinn-bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bookinn-primary);
}

.bookinn-activity-content {
    flex: 1;
}

.bookinn-activity-content p {
    margin: 0 0 var(--bookinn-space-1) 0;
    font-weight: 500;
    color: var(--bookinn-text-primary);
}

.bookinn-activity-time {
    font-size: var(--bookinn-text-xs);
    color: var(--bookinn-text-secondary);
}

/* ===== SYNC STATUS STYLES ===== */
.bookinn-sync-status {
    display: flex;
    flex-direction: column;
    gap: var(--bookinn-space-4);
}

.bookinn-sync-item {
    background: var(--bookinn-bg-primary);
    border: 1px solid var(--bookinn-border-light);
    border-radius: var(--bookinn-radius);
    padding: var(--bookinn-space-4);
}

.bookinn-sync-channel {
    font-weight: 600;
    color: var(--bookinn-text-primary);
    margin-bottom: var(--bookinn-space-2);
}

.bookinn-sync-progress {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-3);
}

.bookinn-progress-bar {
    flex: 1;
    height: 8px;
    background: var(--bookinn-bg-secondary);
    border-radius: var(--bookinn-radius);
    overflow: hidden;
}

.bookinn-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--bookinn-primary) 0%, var(--bookinn-primary-dark) 100%);
    transition: width 0.3s ease;
}

.bookinn-sync-text {
    font-size: var(--bookinn-text-sm);
    font-weight: 500;
    color: var(--bookinn-text-secondary);
    min-width: 80px;
}

/* ===== BUTTON SIZES ===== */
.bookinn-btn-sm {
    padding: var(--bookinn-space-1) var(--bookinn-space-2);
    font-size: 11px;
    min-height: 28px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .bookinn-vertical-tabs {
        flex-direction: column;
    }

    .bookinn-vertical-nav {
        flex: none;
        display: flex;
        overflow-x: auto;
        padding: var(--bookinn-space-2);
    }

    .bookinn-vertical-tab-link {
        white-space: nowrap;
        margin-right: var(--bookinn-space-2);
        margin-bottom: 0;
    }

    .bookinn-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--bookinn-space-2);
    }

    .bookinn-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--bookinn-space-2);
    }

    .bookinn-form-grid {
        grid-template-columns: 1fr;
    }

    .bookinn-metrics-grid {
        grid-template-columns: 1fr;
    }

    .bookinn-tab-content {
        padding: var(--bookinn-space-4);
    }

    .bookinn-vertical-panel {
        padding: var(--bookinn-space-4);
    }
}
